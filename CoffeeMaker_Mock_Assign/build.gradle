/*
 * This build file was generated by the Gradle 'init' task.
 *
 * This generated file contains a sample Java Library project to get you started.
 * For more details take a look at the Java Libraries chapter in the Gradle
 * user guide available at https://docs.gradle.org/3.5/userguide/java_library_plugin.html
 *
 * Extended by <PERSON> for Coursera "Introduction to Software Testing" course.
 * 8/2/2017  
 */


// Apply the java-library plugin to add support for Java Library
apply plugin: 'java-library'
apply plugin: 'jacoco'


// Make sure we are using Java 8.
if(JavaVersion.current() != JavaVersion.VERSION_1_8) 
    throw new GradleException("This project requires Java 8 (Java 1.8), but it's running on "+JavaVersion.current())

sourceCompatibility = JavaVersion.VERSION_1_8
targetCompatibility = JavaVersion.VERSION_1_8 


// In this section you declare where to find the dependencies of your project
repositories {
    // Use jcenter for resolving your dependencies.
    // You can declare any Maven/Ivy/file repository here.
    jcenter()
    mavenCentral()
}

project.ext {
    junitVersion = '4.12'
}


dependencies {
    // Use JUnit test framework
    testImplementation 'junit:junit:4.12'

    // also use Mockito
    testCompile "org.mockito:mockito-core:2.12.0"
}

jacoco {
    toolVersion = "0.7.6.201602180812"
}

check.dependsOn jacocoTestReport
