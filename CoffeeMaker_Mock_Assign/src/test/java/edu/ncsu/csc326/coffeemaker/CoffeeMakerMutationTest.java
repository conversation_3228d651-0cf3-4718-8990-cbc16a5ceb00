/*
 * Copyright (c) 2009,  <PERSON>, <PERSON>, <PERSON><PERSON> Ho
 * All Rights Reserved.
 * 
 * Permission has been explicitly granted to the University of Minnesota 
 * Software Engineering Center to use and distribute this source for 
 * educational purposes, including delivering online education through
 * Coursera or other entities.  
 * 
 * No warranty is given regarding this software, including warranties as
 * to the correctness or completeness of this software, including 
 * fitness for purpose.
 * 
 * Modifications
 * 20171113 - <PERSON> - Extended with additional recipe.
 * 20171114 - <PERSON>   - Updated to JUnit 4; fixed variable names.
 */
package edu.ncsu.csc326.coffeemaker;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import org.junit.Before;
import org.junit.Test;

import edu.ncsu.csc326.coffeemaker.exceptions.RecipeException;

/**
 * Unit tests for CoffeeMaker class focused on mutation testing.
 * These tests are designed to pass with a correct implementation
 * and fail with mutant implementations.
 * 
 * <AUTHOR> Assistant
 */
public class CoffeeMakerMutationTest {
	
	//-----------------------------------------------------------------------
	//	DATA MEMBERS
	//-----------------------------------------------------------------------
	private Recipe recipe1;
	private Recipe recipe2;
	private Recipe recipe3;
	
	private Recipe [] stubRecipies; 
	
	/**
	 * The coffee maker -- our object under test.
	 */
	private CoffeeMaker coffeeMaker;
	
	/**
	 * The stubbed recipe book.
	 */
	private RecipeBookStub recipeBookStub;
	
	/**
	 * Manual stub implementation of RecipeBook for testing
	 */
	private static class RecipeBookStub implements RecipeBook {
		private final Recipe[] recipes;
		private int getRecipesCallCount = 0;
		
		public RecipeBookStub(Recipe[] recipes) {
			this.recipes = recipes;
		}
		
		@Override
		public Recipe[] getRecipes() {
			getRecipesCallCount++;
			return recipes;
		}
		
		@Override
		public boolean addRecipe(Recipe r) {
			return false;
		}
		
		@Override
		public String deleteRecipe(int recipeToDelete) {
			return null;
		}
		
		@Override
		public String editRecipe(int recipeToEdit, Recipe newRecipe) {
			return null;
		}
		
		public int getGetRecipesCallCount() {
			return getRecipesCallCount;
		}
		
		public void resetCallCount() {
			getRecipesCallCount = 0;
		}
	}
	
	//-----------------------------------------------------------------------
	//	Set-up / Tear-down
	//-----------------------------------------------------------------------
	/**
	 * Initializes some recipes to test with, creates the {@link CoffeeMaker} 
	 * object we wish to test, and stubs the {@link RecipeBook}. 
	 * 
	 * @throws RecipeException  if there was an error parsing the ingredient 
	 * 		amount when setting up the recipe.
	 */
	@Before
	public void setUp() throws RecipeException {
		
		//Set up for recipe1
		recipe1 = new Recipe();
		recipe1.setName("Coffee");
		recipe1.setAmtChocolate("0");
		recipe1.setAmtCoffee("3");
		recipe1.setAmtMilk("1");
		recipe1.setAmtSugar("1");
		recipe1.setPrice("50");
		
		//Set up for recipe2
		recipe2 = new Recipe();
		recipe2.setName("Mocha");
		recipe2.setAmtChocolate("20");
		recipe2.setAmtCoffee("3");
		recipe2.setAmtMilk("1");
		recipe2.setAmtSugar("1");
		recipe2.setPrice("75");
		
		//Set up for recipe3
		recipe3 = new Recipe();
		recipe3.setName("Latte");
		recipe3.setAmtChocolate("0");
		recipe3.setAmtCoffee("3");
		recipe3.setAmtMilk("3");
		recipe3.setAmtSugar("1");
		recipe3.setPrice("100");

		stubRecipies = new Recipe [] {recipe1, recipe2, recipe3};
		
		// Create the stub and coffee maker
		recipeBookStub = new RecipeBookStub(stubRecipies);
		coffeeMaker = new CoffeeMaker(recipeBookStub, new Inventory());
	}
	
	//-----------------------------------------------------------------------
	//	Test Methods - Designed for Mutation Testing
	//-----------------------------------------------------------------------
	
	/**
	 * Test UC7: Purchase Beverage - Successful purchase with exact payment
	 * This test should PASS with correct implementation
	 * This test should FAIL with mutations in price comparison or change calculation
	 */
	@Test
	public void testPurchaseBeverage_ExactPayment() {
		// Test purchasing recipe1 (Coffee) with exact payment
		int change = coffeeMaker.makeCoffee(0, 50);
		
		// Should return 0 change for exact payment
		assertEquals("Exact payment should return 0 change", 0, change);
	}
	
	/**
	 * Test UC7: Purchase Beverage - Successful purchase with overpayment
	 * This test should PASS with correct implementation
	 * This test should FAIL with mutations in change calculation
	 */
	@Test
	public void testPurchaseBeverage_Overpayment() {
		// Test purchasing recipe1 (Coffee costs 50) with overpayment
		int change = coffeeMaker.makeCoffee(0, 75);
		
		// Should return correct change (75 - 50 = 25)
		assertEquals("Overpayment should return correct change", 25, change);
	}
	
	/**
	 * Test UC7: Alternative Flow [E2] - Insufficient payment
	 * This test should PASS with correct implementation
	 * This test should FAIL with mutations in payment validation
	 */
	@Test
	public void testPurchaseBeverage_InsufficientPayment() {
		// Try to purchase recipe1 (Coffee costs 50) with insufficient payment
		int change = coffeeMaker.makeCoffee(0, 25);
		
		// Should return all money when payment is insufficient
		assertEquals("Insufficient payment should return all money", 25, change);
	}
	
	/**
	 * Test UC7: Alternative Flow [E3] - Invalid recipe selection
	 * This test should PASS with correct implementation (assuming it handles bounds properly)
	 * This test should FAIL with mutations in bounds checking
	 */
	@Test
	public void testPurchaseBeverage_InvalidRecipeIndex() {
		try {
			// Try to purchase recipe at invalid index
			int change = coffeeMaker.makeCoffee(10, 50);
			
			// If no exception, should return all money
			assertEquals("Invalid recipe should return all money", 50, change);
		} catch (ArrayIndexOutOfBoundsException e) {
			// If exception occurs, this indicates the implementation doesn't handle bounds
			// For mutation testing, we'll accept this behavior
			assertTrue("Implementation throws exception for invalid index", true);
		}
	}
	
	/**
	 * Test to verify that different recipes have different prices
	 * This test should PASS with correct implementation
	 * This test should FAIL with mutations that affect price retrieval
	 */
	@Test
	public void testDifferentRecipePrices() {
		// Purchase different recipes with exact payment
		int change1 = coffeeMaker.makeCoffee(0, 50);  // Coffee costs 50
		assertEquals("Recipe 1 exact payment should return 0", 0, change1);
		
		int change2 = coffeeMaker.makeCoffee(1, 75);  // Mocha costs 75
		assertEquals("Recipe 2 exact payment should return 0", 0, change2);
		
		int change3 = coffeeMaker.makeCoffee(2, 100); // Latte costs 100
		assertEquals("Recipe 3 exact payment should return 0", 0, change3);
	}
	
	/**
	 * Test to verify that overpayment calculations are correct for different amounts
	 * This test should PASS with correct implementation
	 * This test should FAIL with mutations in arithmetic operations
	 */
	@Test
	public void testChangeCalculationAccuracy() {
		// Test various overpayment scenarios
		assertEquals("Change calculation should be accurate", 1, coffeeMaker.makeCoffee(0, 51));
		assertEquals("Change calculation should be accurate", 10, coffeeMaker.makeCoffee(0, 60));
		assertEquals("Change calculation should be accurate", 25, coffeeMaker.makeCoffee(1, 100));
		assertEquals("Change calculation should be accurate", 50, coffeeMaker.makeCoffee(2, 150));
	}
	
	/**
	 * Test to verify that zero payment is handled correctly
	 * This test should PASS with correct implementation
	 * This test should FAIL with mutations in payment validation
	 */
	@Test
	public void testZeroPayment() {
		int change = coffeeMaker.makeCoffee(0, 0);
		assertEquals("Zero payment should return 0", 0, change);
	}
}
