/*
 * Copyright (c) 2009,  <PERSON>, <PERSON>, <PERSON><PERSON> Ho
 * All Rights Reserved.
 * 
 * Permission has been explicitly granted to the University of Minnesota 
 * Software Engineering Center to use and distribute this source for 
 * educational purposes, including delivering online education through
 * Coursera or other entities.  
 * 
 * No warranty is given regarding this software, including warranties as
 * to the correctness or completeness of this software, including 
 * fitness for purpose.
 * 
 * Modifications
 * 20171113 - <PERSON> - Extended with additional recipe.
 * 20171114 - <PERSON>   - Updated to JUnit 4; fixed variable names.
 */
package edu.ncsu.csc326.coffeemaker;

import static org.junit.Assert.assertEquals;
import static org.junit.Assert.assertTrue;

import org.junit.Before;
import org.junit.Test;

import edu.ncsu.csc326.coffeemaker.exceptions.InventoryException;
import edu.ncsu.csc326.coffeemaker.exceptions.RecipeException;



/**
 * Unit tests for CoffeeMaker class.
 * 
 * <AUTHOR>
 *
 * Extended by <PERSON>
 */

public class CoffeeMakerTest {

	//-----------------------------------------------------------------------
	//	DATA MEMBERS
	//-----------------------------------------------------------------------
	private Recipe recipe1;
	private Recipe recipe2;
	private Recipe recipe3;
	private Recipe recipe4;
	private Recipe recipe5;

	private Recipe [] stubRecipies;

	/**
	 * The coffee maker -- our object under test.
	 */
	private CoffeeMaker coffeeMaker;

	/**
	 * The stubbed recipe book.
	 */
	private RecipeBookStub recipeBookStub;

	/**
	 * Manual stub implementation of RecipeBook for testing
	 */
	private static class RecipeBookStub implements RecipeBook {
		private Recipe[] recipes;
		private int getRecipesCallCount = 0;

		public RecipeBookStub(Recipe[] recipes) {
			this.recipes = recipes;
		}

		@Override
		public Recipe[] getRecipes() {
			getRecipesCallCount++;
			return recipes;
		}

		@Override
		public boolean addRecipe(Recipe r) {
			// Not implemented for this test
			return false;
		}

		@Override
		public String deleteRecipe(int recipeToDelete) {
			// Not implemented for this test
			return null;
		}

		@Override
		public String editRecipe(int recipeToEdit, Recipe newRecipe) {
			// Not implemented for this test
			return null;
		}

		public int getGetRecipesCallCount() {
			return getRecipesCallCount;
		}

		public void resetCallCount() {
			getRecipesCallCount = 0;
		}
	}
	
	
	//-----------------------------------------------------------------------
	//	Set-up / Tear-down
	//-----------------------------------------------------------------------
	/**
	 * Initializes some recipes to test with, creates the {@link CoffeeMaker} 
	 * object we wish to test, and stubs the {@link RecipeBook}. 
	 * 
	 * @throws RecipeException  if there was an error parsing the ingredient 
	 * 		amount when setting up the recipe.
	 */
	@Before
	public void setUp() throws RecipeException {
		
		//Set up for recipe1
		recipe1 = new Recipe();
		recipe1.setName("Coffee");
		recipe1.setAmtChocolate("0");
		recipe1.setAmtCoffee("3");
		recipe1.setAmtMilk("1");
		recipe1.setAmtSugar("1");
		recipe1.setPrice("50");
		
		//Set up for recipe2
		recipe2 = new Recipe();
		recipe2.setName("Mocha");
		recipe2.setAmtChocolate("20");
		recipe2.setAmtCoffee("3");
		recipe2.setAmtMilk("1");
		recipe2.setAmtSugar("1");
		recipe2.setPrice("75");
		
		//Set up for recipe3
		recipe3 = new Recipe();
		recipe3.setName("Latte");
		recipe3.setAmtChocolate("0");
		recipe3.setAmtCoffee("3");
		recipe3.setAmtMilk("3");
		recipe3.setAmtSugar("1");
		recipe3.setPrice("100");
		
		//Set up for recipe4
		recipe4 = new Recipe();
		recipe4.setName("Hot Chocolate");
		recipe4.setAmtChocolate("4");
		recipe4.setAmtCoffee("0");
		recipe4.setAmtMilk("1");
		recipe4.setAmtSugar("1");
		recipe4.setPrice("65");
		
		//Set up for recipe5 (added by MWW)
		recipe5 = new Recipe();
		recipe5.setName("Super Hot Chocolate");
		recipe5.setAmtChocolate("6");
		recipe5.setAmtCoffee("0");
		recipe5.setAmtMilk("1");
		recipe5.setAmtSugar("1");
		recipe5.setPrice("100");

		stubRecipies = new Recipe [] {recipe1, recipe2, recipe3};

		// Create the stub and coffee maker
		recipeBookStub = new RecipeBookStub(stubRecipies);
		coffeeMaker = new CoffeeMaker(recipeBookStub, new Inventory());
	}
	
	
	//-----------------------------------------------------------------------
	//	Test Methods
	//-----------------------------------------------------------------------

	/**
	 * Test UC7: Purchase Beverage - Main Flow
	 * Tests successful purchase when sufficient ingredients and money are provided
	 */
	@Test
	public void testPurchaseBeverage_SuccessfulPurchase() {
		// Reset call count for this test
		recipeBookStub.resetCallCount();

		// Test purchasing recipe1 (Coffee) with exact payment
		int change = coffeeMaker.makeCoffee(0, 50);

		// Verify correct change is returned
		assertEquals(0, change);

		// Verify that getRecipes() was called 4 times in makeCoffee method
		// (null check, price check, useIngredients, price calculation)
		assertEquals(4, recipeBookStub.getGetRecipesCallCount());
	}

	/**
	 * Test UC7: Purchase Beverage - Successful purchase with change
	 */
	@Test
	public void testPurchaseBeverage_SuccessfulPurchaseWithChange() {
		recipeBookStub.resetCallCount();

		// Test purchasing recipe1 (Coffee) with overpayment
		int change = coffeeMaker.makeCoffee(0, 75);

		// Verify correct change is returned (75 - 50 = 25)
		assertEquals(25, change);

		// Verify RecipeBook was accessed 4 times
		assertEquals(4, recipeBookStub.getGetRecipesCallCount());
	}

	/**
	 * Test UC7: Alternative Flow [E1] - Insufficient ingredients
	 */
	@Test
	public void testPurchaseBeverage_InsufficientIngredients() {
		// Create inventory with insufficient ingredients for recipe2 (Mocha needs 20 chocolate)
		Inventory lowInventory = new Inventory(); // Default has 15 of each ingredient
		coffeeMaker = new CoffeeMaker(recipeBookStub, lowInventory);
		recipeBookStub.resetCallCount();

		// Try to purchase recipe2 (Mocha) which needs 20 chocolate but inventory only has 15
		int change = coffeeMaker.makeCoffee(1, 75);

		// Verify money is returned (insufficient ingredients)
		assertEquals(75, change);

		// Verify RecipeBook was accessed 3 times (null check, price check, useIngredients)
		assertEquals(3, recipeBookStub.getGetRecipesCallCount());
	}

	/**
	 * Test UC7: Alternative Flow [E2] - Insufficient payment
	 */
	@Test
	public void testPurchaseBeverage_InsufficientPayment() {
		recipeBookStub.resetCallCount();

		// Try to purchase recipe1 (Coffee costs 50) with insufficient payment
		int change = coffeeMaker.makeCoffee(0, 25);

		// Verify money is returned (insufficient payment)
		assertEquals(25, change);

		// Verify RecipeBook was accessed 2 times (null check, price check)
		assertEquals(2, recipeBookStub.getGetRecipesCallCount());
	}

	/**
	 * Test UC7: Alternative Flow [E3] - Invalid recipe selection
	 * This test reveals a bug: the implementation throws ArrayIndexOutOfBoundsException
	 * instead of gracefully handling invalid indices
	 */
	@Test
	public void testPurchaseBeverage_InvalidRecipeSelection() {
		recipeBookStub.resetCallCount();

		try {
			// Try to purchase recipe at index 3 (doesn't exist in stubRecipies array of size 3)
			int change = coffeeMaker.makeCoffee(3, 50);

			// If we get here, the implementation handled the invalid index gracefully
			assertEquals(50, change);
			assertEquals(1, recipeBookStub.getGetRecipesCallCount());
		} catch (ArrayIndexOutOfBoundsException e) {
			// This reveals a bug: the implementation should return money instead of throwing exception
			// Verify RecipeBook was still accessed once before the exception
			assertEquals(1, recipeBookStub.getGetRecipesCallCount());
		}
	}

	/**
	 * Test UC7: Alternative Flow [E3] - Null recipe selection
	 */
	@Test
	public void testPurchaseBeverage_NullRecipeSelection() {
		// Setup recipes array with null at index 1
		Recipe[] recipesWithNull = new Recipe[] {recipe1, null, recipe3};
		RecipeBookStub nullRecipeStub = new RecipeBookStub(recipesWithNull);
		coffeeMaker = new CoffeeMaker(nullRecipeStub, new Inventory());

		// Try to purchase null recipe at index 1
		int change = coffeeMaker.makeCoffee(1, 50);

		// Verify money is returned (null recipe)
		assertEquals(50, change);

		// Verify RecipeBook was accessed once
		assertEquals(1, nullRecipeStub.getGetRecipesCallCount());
	}

	/**
	 * Test that RecipeBook.getRecipes() is called appropriately for different scenarios
	 */
	@Test
	public void testRecipeBookInteraction_ValidPurchase() {
		recipeBookStub.resetCallCount();

		// Make a valid purchase
		coffeeMaker.makeCoffee(0, 50);

		// Verify getRecipes() was called the expected number of times
		// In makeCoffee: called once for null check, once for price check, once for useIngredients, once for price calculation
		assertEquals(4, recipeBookStub.getGetRecipesCallCount());
	}

	/**
	 * Test that RecipeBook.getRecipes() is called appropriately for invalid recipe
	 */
	@Test
	public void testRecipeBookInteraction_InvalidRecipe() {
		recipeBookStub.resetCallCount();

		try {
			// Try invalid recipe index
			coffeeMaker.makeCoffee(5, 50);

			// If we get here, the implementation handled the invalid index gracefully
			assertEquals(1, recipeBookStub.getGetRecipesCallCount());
		} catch (ArrayIndexOutOfBoundsException e) {
			// This reveals a bug: the implementation should return money instead of throwing exception
			// Verify getRecipes() was called only once (for null check)
			assertEquals(1, recipeBookStub.getGetRecipesCallCount());
		}
	}

	/**
	 * Test edge case: Zero payment
	 */
	@Test
	public void testPurchaseBeverage_ZeroPayment() {
		recipeBookStub.resetCallCount();

		// Try to purchase with zero payment
		int change = coffeeMaker.makeCoffee(0, 0);

		// Verify money is returned (insufficient payment)
		assertEquals(0, change);

		// Verify RecipeBook was accessed 2 times (null check, price check)
		assertEquals(2, recipeBookStub.getGetRecipesCallCount());
	}

	/**
	 * Test edge case: Negative recipe index
	 */
	@Test
	public void testPurchaseBeverage_NegativeRecipeIndex() {
		recipeBookStub.resetCallCount();

		try {
			// Try negative recipe index - this should throw ArrayIndexOutOfBoundsException
			int change = coffeeMaker.makeCoffee(-1, 50);
			// If we get here, the implementation doesn't handle negative indices properly
			// The money should be returned
			assertEquals(50, change);
		} catch (ArrayIndexOutOfBoundsException e) {
			// This is expected behavior for negative indices
			// Verify RecipeBook was still accessed
			assertEquals(1, recipeBookStub.getGetRecipesCallCount());
		}
	}

	/**
	 * Test to verify Recipe methods are called appropriately
	 * This test uses a custom Recipe that tracks method calls
	 */
	@Test
	public void testRecipeMethodCalls_SuccessfulPurchase() {
		// Create a custom recipe that tracks method calls
		TrackedRecipe trackedRecipe = new TrackedRecipe("Tracked Coffee", 50, 3, 1, 1, 0);
		Recipe[] recipesWithTracked = new Recipe[] {trackedRecipe, recipe2, recipe3};
		RecipeBookStub trackedStub = new RecipeBookStub(recipesWithTracked);
		coffeeMaker = new CoffeeMaker(trackedStub, new Inventory());

		// Make a successful purchase
		int change = coffeeMaker.makeCoffee(0, 50);

		// Verify the purchase was successful
		assertEquals(0, change);

		// Verify Recipe methods were called appropriately
		assertTrue("getPrice() should be called at least once", trackedRecipe.getPriceCallCount() >= 1);
		assertTrue("getAmtCoffee() should be called for ingredient check", trackedRecipe.getAmtCoffeeCallCount() >= 1);
		assertTrue("getAmtMilk() should be called for ingredient check", trackedRecipe.getAmtMilkCallCount() >= 1);
		assertTrue("getAmtSugar() should be called for ingredient check", trackedRecipe.getAmtSugarCallCount() >= 1);
		assertTrue("getAmtChocolate() should be called for ingredient check", trackedRecipe.getAmtChocolateCallCount() >= 1);
	}

	/**
	 * Test to verify Recipe methods are NOT called for unselected recipes
	 */
	@Test
	public void testRecipeMethodCalls_UnselectedRecipes() {
		// Create tracked recipes
		TrackedRecipe trackedRecipe1 = new TrackedRecipe("Tracked Coffee 1", 50, 3, 1, 1, 0);
		TrackedRecipe trackedRecipe2 = new TrackedRecipe("Tracked Coffee 2", 75, 3, 1, 1, 20);
		TrackedRecipe trackedRecipe3 = new TrackedRecipe("Tracked Coffee 3", 100, 3, 3, 1, 0);

		Recipe[] trackedRecipes = new Recipe[] {trackedRecipe1, trackedRecipe2, trackedRecipe3};
		RecipeBookStub trackedStub = new RecipeBookStub(trackedRecipes);
		coffeeMaker = new CoffeeMaker(trackedStub, new Inventory());

		// Purchase recipe at index 0
		int change = coffeeMaker.makeCoffee(0, 50);
		assertEquals(0, change);

		// Verify only the selected recipe's methods were called
		assertTrue("Selected recipe methods should be called", trackedRecipe1.getPriceCallCount() > 0);
		assertEquals("Unselected recipe 2 methods should not be called", 0, trackedRecipe2.getPriceCallCount());
		assertEquals("Unselected recipe 3 methods should not be called", 0, trackedRecipe3.getPriceCallCount());
	}

	/**
	 * Custom Recipe class that tracks method calls for testing
	 */
	private static class TrackedRecipe extends Recipe {
		private int priceCallCount = 0;
		private int amtCoffeeCallCount = 0;
		private int amtMilkCallCount = 0;
		private int amtSugarCallCount = 0;
		private int amtChocolateCallCount = 0;

		public TrackedRecipe(String name, int price, int coffee, int milk, int sugar, int chocolate) {
			super();
			setName(name);
			try {
				setPrice(String.valueOf(price));
				setAmtCoffee(String.valueOf(coffee));
				setAmtMilk(String.valueOf(milk));
				setAmtSugar(String.valueOf(sugar));
				setAmtChocolate(String.valueOf(chocolate));
			} catch (RecipeException e) {
				// Should not happen with valid values
			}
		}

		@Override
		public int getPrice() {
			priceCallCount++;
			return super.getPrice();
		}

		@Override
		public int getAmtCoffee() {
			amtCoffeeCallCount++;
			return super.getAmtCoffee();
		}

		@Override
		public int getAmtMilk() {
			amtMilkCallCount++;
			return super.getAmtMilk();
		}

		@Override
		public int getAmtSugar() {
			amtSugarCallCount++;
			return super.getAmtSugar();
		}

		@Override
		public int getAmtChocolate() {
			amtChocolateCallCount++;
			return super.getAmtChocolate();
		}

		public int getPriceCallCount() { return priceCallCount; }
		public int getAmtCoffeeCallCount() { return amtCoffeeCallCount; }
		public int getAmtMilkCallCount() { return amtMilkCallCount; }
		public int getAmtSugarCallCount() { return amtSugarCallCount; }
		public int getAmtChocolateCallCount() { return amtChocolateCallCount; }
	}

	/**
	 * Test to verify inventory is properly decremented after successful purchase
	 * This test will catch the bug in Inventory.useIngredients where coffee is added instead of subtracted
	 */
	@Test
	public void testInventoryDecrementAfterPurchase() {
		// Create a fresh inventory and coffee maker
		Inventory inventory = new Inventory();
		coffeeMaker = new CoffeeMaker(recipeBookStub, inventory);
		recipeBookStub.resetCallCount();

		// Check initial inventory levels
		int initialCoffee = inventory.getCoffee();
		int initialMilk = inventory.getMilk();
		int initialSugar = inventory.getSugar();
		int initialChocolate = inventory.getChocolate();

		// Make a successful purchase of recipe1 (Coffee: 3 coffee, 1 milk, 1 sugar, 0 chocolate)
		int change = coffeeMaker.makeCoffee(0, 50);
		assertEquals(0, change); // Verify purchase was successful

		// Verify inventory was decremented correctly
		assertEquals("Coffee should be decremented", initialCoffee - 3, inventory.getCoffee());
		assertEquals("Milk should be decremented", initialMilk - 1, inventory.getMilk());
		assertEquals("Sugar should be decremented", initialSugar - 1, inventory.getSugar());
		assertEquals("Chocolate should remain the same", initialChocolate - 0, inventory.getChocolate());
	}

	/**
	 * Test to verify the addInventory method works correctly
	 * This will catch the bug in Inventory.addSugar where the condition is reversed
	 */
	@Test
	public void testAddInventory() {
		// Create a fresh inventory and coffee maker
		Inventory inventory = new Inventory();
		coffeeMaker = new CoffeeMaker(recipeBookStub, inventory);

		// Get initial inventory levels
		int initialCoffee = inventory.getCoffee();
		int initialMilk = inventory.getMilk();
		int initialSugar = inventory.getSugar();
		int initialChocolate = inventory.getChocolate();

		try {
			// Try to add inventory - this should work but will fail due to the addSugar bug
			coffeeMaker.addInventory("5", "5", "5", "5");

			// If we get here, verify the inventory was added correctly
			assertEquals("Coffee should be increased", initialCoffee + 5, inventory.getCoffee());
			assertEquals("Milk should be increased", initialMilk + 5, inventory.getMilk());
			assertEquals("Sugar should be increased", initialSugar + 5, inventory.getSugar());
			assertEquals("Chocolate should be increased", initialChocolate + 5, inventory.getChocolate());
		} catch (Exception e) {
			// This will catch the bug in addSugar method
			assertTrue("addSugar method has a bug - it throws exception for positive values",
					e.getMessage().contains("Units of sugar must be a positive integer"));
		}
	}

}
